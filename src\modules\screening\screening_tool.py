"""
筛查工具基础类和性能参数管理

实现筛查工具的基础架构，包括性能参数、成本模型和工具特异性实现。
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, Optional, Any, List, Union
import logging

from src.core.enums import DiseaseState, AnatomicalLocation
from src.core.individual import Individual
from .enums import ScreeningToolType, ScreeningResult, InvasivenessLevel, OperatorSkillLevel


logger = logging.getLogger(__name__)


@dataclass
class ScreeningPerformance:
    """筛查工具性能参数配置"""
    
    # 疾病状态特异性敏感性映射
    sensitivity_by_state: Dict[DiseaseState, float] = field(default_factory=dict)
    
    # 总体特异性
    specificity: float = 0.95
    
    # 检测阈值（如适用）
    detection_threshold: Optional[float] = None
    
    # 操作者依赖性因子（1.0为标准，>1.0表示更依赖操作者技能）
    operator_dependency: float = 1.0
    
    # 解剖位置特异性敏感性调整因子
    location_sensitivity_modifiers: Dict[AnatomicalLocation, float] = field(
        default_factory=lambda: {
            AnatomicalLocation.PROXIMAL_COLON: 1.0,
            AnatomicalLocation.DISTAL_COLON: 1.0,
            AnatomicalLocation.RECTUM: 1.0
        }
    )
    
    # 年龄相关性能调整
    age_sensitivity_adjustment: Dict[str, float] = field(
        default_factory=lambda: {
            "slope": 0.0,      # 每年敏感性变化率
            "baseline_age": 50.0,  # 基准年龄
            "min_sensitivity": 0.1,  # 最小敏感性
            "max_sensitivity": 1.0   # 最大敏感性
        }
    )
    
    # 性别特异性调整
    gender_sensitivity_modifiers: Dict[str, float] = field(
        default_factory=lambda: {
            "male": 1.0,
            "female": 1.0
        }
    )

    def __post_init__(self):
        """验证性能参数的有效性"""
        # 验证特异性范围
        if not 0.0 <= self.specificity <= 1.0:
            raise ValueError(f"特异性必须在0-1范围内，当前值: {self.specificity}")
        
        # 验证敏感性范围
        for state, sensitivity in self.sensitivity_by_state.items():
            if not 0.0 <= sensitivity <= 1.0:
                raise ValueError(f"状态 {state} 的敏感性必须在0-1范围内，当前值: {sensitivity}")
        
        # 验证操作者依赖性
        if not 0.1 <= self.operator_dependency <= 5.0:
            raise ValueError(f"操作者依赖性必须在0.1-5.0范围内，当前值: {self.operator_dependency}")


@dataclass
class ScreeningCharacteristics:
    """筛查工具特性配置"""
    
    invasiveness: InvasivenessLevel = InvasivenessLevel.NON_INVASIVE
    operator_skill_required: OperatorSkillLevel = OperatorSkillLevel.LOW
    preparation_required: bool = False
    turnaround_time_days: int = 0
    
    # 解剖位置检测能力
    can_detect_proximal: bool = True
    can_detect_distal: bool = True
    can_detect_rectal: bool = True
    
    # 其他特性
    requires_sedation: bool = False
    radiation_exposure: bool = False
    sample_collection_required: bool = False


class ScreeningTool(ABC):
    """筛查工具基础抽象类"""
    
    def __init__(
        self,
        tool_type: ScreeningToolType,
        performance: ScreeningPerformance,
        characteristics: ScreeningCharacteristics,
        config: Optional[Dict[str, Any]] = None
    ):
        self.tool_type = tool_type
        self.performance = performance
        self.characteristics = characteristics
        self.config = config or {}
        
        # 验证配置
        self._validate_configuration()
    
    def _validate_configuration(self) -> None:
        """验证工具配置的完整性"""
        # 检查必需的敏感性配置
        required_states = [
            DiseaseState.NORMAL,
            DiseaseState.LOW_RISK_ADENOMA,
            DiseaseState.HIGH_RISK_ADENOMA,
            DiseaseState.PRECLINICAL_CANCER,
            DiseaseState.CLINICAL_CANCER_STAGE_I
        ]
        
        missing_states = []
        for state in required_states:
            if state not in self.performance.sensitivity_by_state:
                missing_states.append(state)
        
        if missing_states:
            logger.warning(f"筛查工具 {self.tool_type} 缺少以下状态的敏感性配置: {missing_states}")
    
    def calculate_detection_probability(
        self,
        individual: Individual,
        current_state: DiseaseState,
        anatomical_location: Optional[AnatomicalLocation] = None
    ) -> float:
        """
        计算对特定个体和疾病状态的检测概率
        
        Args:
            individual: 个体对象
            current_state: 当前疾病状态
            anatomical_location: 解剖位置（可选）
            
        Returns:
            float: 检测概率 (0-1)
        """
        # 获取基础敏感性
        base_sensitivity = self.performance.sensitivity_by_state.get(current_state, 0.0)
        
        if base_sensitivity == 0.0:
            return 0.0
        
        # 应用年龄调整
        age_factor = self._calculate_age_adjustment(individual.get_current_age())
        
        # 应用性别调整
        gender_factor = self._calculate_gender_adjustment(individual.gender)
        
        # 应用解剖位置调整
        location_factor = self._calculate_location_adjustment(anatomical_location)
        
        # 应用操作者技能调整
        operator_factor = self._calculate_operator_adjustment()
        
        # 计算最终检测概率
        final_probability = (
            base_sensitivity * 
            age_factor * 
            gender_factor * 
            location_factor * 
            operator_factor
        )
        
        # 确保概率在有效范围内
        return max(0.0, min(1.0, final_probability))
    
    def _calculate_age_adjustment(self, age: float) -> float:
        """计算年龄相关的敏感性调整因子"""
        age_config = self.performance.age_sensitivity_adjustment
        
        slope = age_config.get("slope", 0.0)
        baseline_age = age_config.get("baseline_age", 50.0)
        min_sensitivity = age_config.get("min_sensitivity", 0.1)
        max_sensitivity = age_config.get("max_sensitivity", 1.0)
        
        # 线性年龄调整
        age_factor = 1.0 + slope * (age - baseline_age)
        
        # 应用边界限制
        return max(min_sensitivity, min(max_sensitivity, age_factor))
    
    def _calculate_gender_adjustment(self, gender: str) -> float:
        """计算性别相关的敏感性调整因子"""
        return self.performance.gender_sensitivity_modifiers.get(gender, 1.0)
    
    def _calculate_location_adjustment(
        self, 
        location: Optional[AnatomicalLocation]
    ) -> float:
        """计算解剖位置相关的敏感性调整因子"""
        if location is None:
            return 1.0
        
        # 检查工具是否能检测该位置
        if location == AnatomicalLocation.PROXIMAL_COLON and not self.characteristics.can_detect_proximal:
            return 0.0
        elif location == AnatomicalLocation.DISTAL_COLON and not self.characteristics.can_detect_distal:
            return 0.0
        elif location == AnatomicalLocation.RECTUM and not self.characteristics.can_detect_rectal:
            return 0.0
        
        return self.performance.location_sensitivity_modifiers.get(location, 1.0)
    
    def _calculate_operator_adjustment(self) -> float:
        """计算操作者技能相关的调整因子"""
        # 简化的操作者技能影响模型
        # operator_dependency表示对操作者技能的依赖程度，但不应该直接降低敏感性
        # 在实际应用中，这个因子应该基于具体的操作者质量数据
        # 这里我们假设标准操作者质量，返回1.0
        return 1.0
    
    def calculate_specificity(self, individual: Individual) -> float:
        """
        计算对特定个体的特异性
        
        Args:
            individual: 个体对象
            
        Returns:
            float: 特异性 (0-1)
        """
        # 基础特异性
        base_specificity = self.performance.specificity
        
        # 可以根据个体特征进行调整
        # 目前返回基础特异性
        return base_specificity
    
    @abstractmethod
    def perform_screening(
        self,
        individual: Individual,
        **kwargs
    ) -> ScreeningResult:
        """
        执行筛查检查
        
        Args:
            individual: 被筛查个体
            **kwargs: 其他参数
            
        Returns:
            ScreeningResult: 筛查结果
        """
        pass
    
    def get_tool_info(self) -> Dict[str, Any]:
        """获取工具基本信息"""
        return {
            "type": self.tool_type.value,
            "display_name": self.tool_type.display_name,
            "invasiveness": self.characteristics.invasiveness.value,
            "operator_skill": self.characteristics.operator_skill_required.value,
            "preparation_required": self.characteristics.preparation_required,
            "turnaround_time_days": self.characteristics.turnaround_time_days,
            "specificity": self.performance.specificity
        }
    
    def __str__(self) -> str:
        return f"{self.tool_type.display_name}"

    def __repr__(self) -> str:
        return f"ScreeningTool(type={self.tool_type}, specificity={self.performance.specificity})"


class ScreeningToolFactory:
    """筛查工具工厂类"""

    _tool_registry: Dict[ScreeningToolType, type] = {}

    @classmethod
    def register_tool(cls, tool_type: ScreeningToolType, tool_class: type) -> None:
        """
        注册筛查工具类

        Args:
            tool_type: 工具类型
            tool_class: 工具实现类
        """
        cls._tool_registry[tool_type] = tool_class
        logger.info(f"已注册筛查工具: {tool_type.display_name} -> {tool_class.__name__}")

    @classmethod
    def create_tool(
        cls,
        tool_type: ScreeningToolType,
        config: Optional[Dict[str, Any]] = None
    ) -> ScreeningTool:
        """
        创建筛查工具实例

        Args:
            tool_type: 工具类型
            config: 配置参数

        Returns:
            ScreeningTool: 工具实例

        Raises:
            ValueError: 如果工具类型未注册
        """
        if tool_type not in cls._tool_registry:
            raise ValueError(f"未注册的筛查工具类型: {tool_type}")

        tool_class = cls._tool_registry[tool_type]
        return tool_class(config=config)

    @classmethod
    def get_available_tools(cls) -> List[ScreeningToolType]:
        """获取所有已注册的工具类型"""
        return list(cls._tool_registry.keys())

    @classmethod
    def is_tool_registered(cls, tool_type: ScreeningToolType) -> bool:
        """检查工具类型是否已注册"""
        return tool_type in cls._tool_registry
