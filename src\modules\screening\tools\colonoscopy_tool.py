"""
结肠镜检查工具实现

实现结肠镜筛查工具的特异性检测逻辑和性能参数。
"""

import random
from typing import Dict, Any, Optional
import logging

from src.core.enums import DiseaseState, AnatomicalLocation
from src.core.individual import Individual
from ..enums import ScreeningToolType, ScreeningResult, InvasivenessLevel, OperatorSkillLevel
from ..screening_tool import ScreeningTool, ScreeningPerformance, ScreeningCharacteristics


logger = logging.getLogger(__name__)


class ColonoscopyTool(ScreeningTool):
    """结肠镜检查工具实现"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化结肠镜工具
        
        Args:
            config: 配置参数字典
        """
        # 默认结肠镜性能参数（基于文献数据）
        default_performance = ScreeningPerformance(
            sensitivity_by_state={
                DiseaseState.NORMAL: 0.0,
                DiseaseState.LOW_RISK_ADENOMA: 0.85,
                DiseaseState.HIGH_RISK_ADENOMA: 0.95,
                DiseaseState.SMALL_SERRATED: 0.75,  # 锯齿状腺瘤检测较困难
                DiseaseState.LARGE_SERRATED: 0.90,
                DiseaseState.PRECLINICAL_CANCER: 0.98,
                DiseaseState.CLINICAL_CANCER_STAGE_I: 0.99,
                DiseaseState.CLINICAL_CANCER_STAGE_II: 0.995,
                DiseaseState.CLINICAL_CANCER_STAGE_III: 0.998,
                DiseaseState.CLINICAL_CANCER_STAGE_IV: 1.0,
                DiseaseState.CLINICAL_CANCER: 0.99,  # 向后兼容
            },
            specificity=0.99,
            detection_threshold=None,  # 结肠镜无特定阈值
            operator_dependency=2.0,   # 高度依赖操作者技能
            location_sensitivity_modifiers={
                AnatomicalLocation.PROXIMAL_COLON: 0.9,   # 近端稍难检测
                AnatomicalLocation.DISTAL_COLON: 1.0,     # 标准检测
                AnatomicalLocation.RECTUM: 1.0            # 标准检测
            },
            age_sensitivity_adjustment={
                "slope": -0.001,       # 年龄增加敏感性略降
                "baseline_age": 50.0,
                "min_sensitivity": 0.7,
                "max_sensitivity": 1.0
            },
            gender_sensitivity_modifiers={
                "male": 1.0,
                "female": 0.95  # 女性检查稍困难
            }
        )
        
        # 默认结肠镜特性
        default_characteristics = ScreeningCharacteristics(
            invasiveness=InvasivenessLevel.INVASIVE,
            operator_skill_required=OperatorSkillLevel.HIGH,
            preparation_required=True,
            turnaround_time_days=0,  # 即时结果
            can_detect_proximal=True,
            can_detect_distal=True,
            can_detect_rectal=True,
            requires_sedation=True,
            radiation_exposure=False,
            sample_collection_required=False
        )
        
        # 合并用户配置
        if config:
            # 处理performance子配置
            performance_config = config.get("performance", {})
            if "sensitivity_by_state" in performance_config:
                default_performance.sensitivity_by_state.update(
                    performance_config["sensitivity_by_state"]
                )
            if "specificity" in performance_config:
                default_performance.specificity = performance_config["specificity"]
            if "operator_dependency" in performance_config:
                default_performance.operator_dependency = performance_config["operator_dependency"]

            # 处理直接配置（向后兼容）
            if "sensitivity_by_state" in config:
                default_performance.sensitivity_by_state.update(
                    config["sensitivity_by_state"]
                )
            if "specificity" in config:
                default_performance.specificity = config["specificity"]
            if "operator_dependency" in config:
                default_performance.operator_dependency = config["operator_dependency"]
        
        super().__init__(
            tool_type=ScreeningToolType.COLONOSCOPY,
            performance=default_performance,
            characteristics=default_characteristics,
            config=config
        )
        
        # 结肠镜特异性配置
        self.cecal_intubation_rate = config.get("cecal_intubation_rate", 0.95) if config else 0.95
        self.adenoma_detection_rate = config.get("adenoma_detection_rate", 0.25) if config else 0.25
        self.withdrawal_time_minutes = config.get("withdrawal_time_minutes", 8.0) if config else 8.0
    
    def perform_screening(
        self,
        individual: Individual,
        **kwargs
    ) -> ScreeningResult:
        """
        执行结肠镜筛查检查
        
        Args:
            individual: 被筛查个体
            **kwargs: 其他参数
            
        Returns:
            ScreeningResult: 筛查结果
        """
        try:
            # 检查是否成功完成检查
            if not self._is_procedure_successful(individual, **kwargs):
                return ScreeningResult.INADEQUATE
            
            # 获取个体当前疾病状态
            current_state = individual.current_disease_state
            anatomical_location = kwargs.get("anatomical_location")
            
            # 计算检测概率
            detection_probability = self.calculate_detection_probability(
                individual=individual,
                current_state=current_state,
                anatomical_location=anatomical_location
            )
            
            # 应用操作者质量调整
            operator_quality = kwargs.get("operator_quality", 1.0)
            adjusted_detection_probability = detection_probability * operator_quality
            
            # 计算特异性
            specificity = self.calculate_specificity(individual)
            
            # 模拟筛查结果
            if current_state == DiseaseState.NORMAL:
                # 正常状态：基于特异性判断是否假阳性
                if random.random() > specificity:
                    result = ScreeningResult.POSITIVE  # 假阳性
                else:
                    result = ScreeningResult.NEGATIVE  # 真阴性
            else:
                # 疾病状态：基于敏感性判断是否检出
                if random.random() < adjusted_detection_probability:
                    result = ScreeningResult.POSITIVE  # 真阳性
                else:
                    result = ScreeningResult.NEGATIVE  # 假阴性
            
            # 记录筛查日志
            logger.debug(
                f"结肠镜筛查结果 - 个体ID: {getattr(individual, 'id', 'unknown')}, "
                f"状态: {current_state}, 结果: {result}, "
                f"检测概率: {adjusted_detection_probability:.3f}"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"结肠镜筛查执行失败: {e}")
            return ScreeningResult.FAILED
    
    def _is_procedure_successful(
        self,
        individual: Individual,
        **kwargs
    ) -> bool:
        """
        判断结肠镜检查是否成功完成
        
        Args:
            individual: 个体对象
            **kwargs: 其他参数
            
        Returns:
            bool: 是否成功完成
        """
        # 基础成功率
        base_success_rate = 0.95
        
        # 年龄调整（老年人成功率稍低）
        age_factor = 1.0
        current_age = individual.get_current_age()
        if current_age > 75:
            age_factor = 0.9
        elif current_age > 85:
            age_factor = 0.8
        
        # 性别调整（女性成功率稍低）
        gender_factor = 0.95 if individual.gender == "female" else 1.0
        
        # 肠道准备质量
        bowel_prep_quality = kwargs.get("bowel_prep_quality", 0.9)
        
        # 操作者经验
        operator_experience = kwargs.get("operator_experience", 1.0)
        
        # 计算最终成功率
        final_success_rate = (
            base_success_rate * 
            age_factor * 
            gender_factor * 
            bowel_prep_quality * 
            operator_experience
        )
        
        return random.random() < final_success_rate
    
    def calculate_cecal_intubation_success(
        self,
        individual: Individual,
        **kwargs
    ) -> bool:
        """
        计算是否成功到达盲肠
        
        Args:
            individual: 个体对象
            **kwargs: 其他参数
            
        Returns:
            bool: 是否成功到达盲肠
        """
        # 基础盲肠插管率
        base_rate = self.cecal_intubation_rate
        
        # 个体因素调整
        age_factor = 0.98 if individual.get_current_age() > 70 else 1.0
        gender_factor = 0.95 if individual.gender == "female" else 1.0
        
        # 操作者因素
        operator_skill = kwargs.get("operator_skill", 1.0)
        
        final_rate = base_rate * age_factor * gender_factor * operator_skill
        
        return random.random() < final_rate
    
    def calculate_withdrawal_time_impact(
        self,
        withdrawal_time: float
    ) -> float:
        """
        计算退镜时间对检测率的影响
        
        Args:
            withdrawal_time: 退镜时间（分钟）
            
        Returns:
            float: 检测率调整因子
        """
        # 基于文献：退镜时间≥6分钟显著提高腺瘤检出率
        if withdrawal_time < 6.0:
            return 0.7  # 检测率显著降低
        elif withdrawal_time < 8.0:
            return 0.9  # 检测率稍降低
        else:
            return 1.0  # 标准检测率
    
    def get_quality_metrics(self) -> Dict[str, Any]:
        """获取结肠镜质量指标"""
        return {
            "cecal_intubation_rate": self.cecal_intubation_rate,
            "adenoma_detection_rate": self.adenoma_detection_rate,
            "withdrawal_time_minutes": self.withdrawal_time_minutes,
            "recommended_withdrawal_time": 6.0,
            "operator_dependency": self.performance.operator_dependency
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取结肠镜工具性能指标"""
        return {
            "tool_type": "COLONOSCOPY",
            "specificity": self.performance.specificity,
            "sensitivity_cancer": self.performance.sensitivity_by_state.get(
                DiseaseState.CLINICAL_CANCER_STAGE_I, 0.0
            ),
            "sensitivity_high_risk_adenoma": self.performance.sensitivity_by_state.get(
                DiseaseState.HIGH_RISK_ADENOMA, 0.0
            ),
            "sensitivity_low_risk_adenoma": self.performance.sensitivity_by_state.get(
                DiseaseState.LOW_RISK_ADENOMA, 0.0
            ),
            "turnaround_time_days": self.characteristics.turnaround_time_days,
            "invasiveness": self.characteristics.invasiveness.value,
            "preparation_required": self.characteristics.preparation_required,
            "requires_sedation": self.characteristics.requires_sedation,
            "cecal_intubation_rate": self.cecal_intubation_rate
        }
    
    def __str__(self) -> str:
        return f"结肠镜工具 (特异性: {self.performance.specificity:.1%}, 盲肠插管率: {self.cecal_intubation_rate:.1%})"
