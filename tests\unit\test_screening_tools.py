"""
筛查工具单元测试

测试筛查工具的创建、配置和基本功能。
"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch

from src.core.enums import DiseaseState, AnatomicalLocation, Gender
from src.core.individual import Individual
from src.modules.screening import (
    ScreeningToolType, ScreeningResult, ScreeningTool, ScreeningPerformance,
    ScreeningCharacteristics, ScreeningToolFactory, FITTool, ColonoscopyTool,
    SigmoidoscopyTool, ScreeningToolConfigManager, ScreeningToolConfigTemplates
)
from src.utils.validators import ScreeningToolValidationError


class TestScreeningToolEnums:
    """测试筛查工具枚举"""
    
    def test_screening_tool_type_display_names(self):
        """测试工具类型显示名称"""
        assert ScreeningToolType.FIT.display_name == "粪便免疫化学检测 (FIT)"
        assert ScreeningToolType.COLONOSCOPY.display_name == "结肠镜检查"
        assert ScreeningToolType.SIGMOIDOSCOPY.display_name == "乙状结肠镜检查"
    
    def test_screening_tool_type_properties(self):
        """测试工具类型属性"""
        # 测试侵入性
        assert not ScreeningToolType.FIT.is_invasive
        assert ScreeningToolType.COLONOSCOPY.is_invasive
        assert ScreeningToolType.SIGMOIDOSCOPY.is_invasive
        
        # 测试准备要求
        assert not ScreeningToolType.FIT.requires_preparation
        assert ScreeningToolType.COLONOSCOPY.requires_preparation
        assert ScreeningToolType.SIGMOIDOSCOPY.requires_preparation
    
    def test_screening_result_properties(self):
        """测试筛查结果属性"""
        assert ScreeningResult.POSITIVE.requires_followup
        assert ScreeningResult.INADEQUATE.requires_followup
        assert not ScreeningResult.NEGATIVE.requires_followup
        assert not ScreeningResult.FAILED.requires_followup


class TestScreeningPerformance:
    """测试筛查性能参数"""
    
    def test_performance_validation(self):
        """测试性能参数验证"""
        # 有效的性能参数
        valid_performance = ScreeningPerformance(
            sensitivity_by_state={DiseaseState.NORMAL: 0.0, DiseaseState.CLINICAL_CANCER_STAGE_I: 0.9},
            specificity=0.95
        )
        assert valid_performance.specificity == 0.95
        
        # 无效的特异性
        with pytest.raises(ValueError):
            ScreeningPerformance(
                sensitivity_by_state={},
                specificity=1.5  # 超出范围
            )
        
        # 无效的敏感性
        with pytest.raises(ValueError):
            ScreeningPerformance(
                sensitivity_by_state={DiseaseState.NORMAL: -0.1},  # 负值
                specificity=0.95
            )


class TestScreeningToolFactory:
    """测试筛查工具工厂"""
    
    def test_tool_registration(self):
        """测试工具注册"""
        # 检查预注册的工具
        available_tools = ScreeningToolFactory.get_available_tools()
        assert ScreeningToolType.FIT in available_tools
        assert ScreeningToolType.COLONOSCOPY in available_tools
        assert ScreeningToolType.SIGMOIDOSCOPY in available_tools
    
    def test_tool_creation(self):
        """测试工具创建"""
        # 创建FIT工具
        fit_tool = ScreeningToolFactory.create_tool(ScreeningToolType.FIT)
        assert isinstance(fit_tool, FITTool)
        assert fit_tool.tool_type == ScreeningToolType.FIT
        
        # 创建结肠镜工具
        colonoscopy_tool = ScreeningToolFactory.create_tool(ScreeningToolType.COLONOSCOPY)
        assert isinstance(colonoscopy_tool, ColonoscopyTool)
        assert colonoscopy_tool.tool_type == ScreeningToolType.COLONOSCOPY
    
    def test_tool_creation_with_config(self):
        """测试带配置的工具创建"""
        config = {
            "specificity": 0.98,
            "sensitivity_by_state": {
                DiseaseState.CLINICAL_CANCER_STAGE_I: 0.95
            }
        }
        
        fit_tool = ScreeningToolFactory.create_tool(ScreeningToolType.FIT, config)
        assert fit_tool.performance.specificity == 0.98
    
    def test_unregistered_tool_error(self):
        """测试未注册工具错误"""
        with pytest.raises(ValueError, match="未注册的筛查工具类型"):
            ScreeningToolFactory.create_tool(ScreeningToolType.OTHER)


class TestFITTool:
    """测试FIT工具"""
    
    @pytest.fixture
    def sample_individual(self):
        """创建样本个体"""
        return Individual(
            birth_year=1973,
            gender=Gender.MALE
        )
    
    @pytest.fixture
    def fit_tool(self):
        """创建FIT工具实例"""
        return FITTool()
    
    def test_fit_tool_initialization(self, fit_tool):
        """测试FIT工具初始化"""
        assert fit_tool.tool_type == ScreeningToolType.FIT
        assert fit_tool.performance.specificity == 0.95
        assert fit_tool.characteristics.invasiveness.value == "non_invasive"
        assert not fit_tool.characteristics.preparation_required
    
    def test_detection_probability_calculation(self, fit_tool, sample_individual):
        """测试检测概率计算"""
        # 正常状态应该返回0
        prob_normal = fit_tool.calculate_detection_probability(
            sample_individual,
            DiseaseState.NORMAL
        )
        assert prob_normal == 0.0
        
        # 癌症状态应该有较高检测概率
        prob_cancer = fit_tool.calculate_detection_probability(
            sample_individual,
            DiseaseState.CLINICAL_CANCER_STAGE_I
        )
        assert prob_cancer > 0.5
    
    def test_location_adjustment(self, fit_tool, sample_individual):
        """测试解剖位置调整"""
        # 近端结肠敏感性较低
        prob_proximal = fit_tool.calculate_detection_probability(
            sample_individual,
            DiseaseState.HIGH_RISK_ADENOMA,
            AnatomicalLocation.PROXIMAL_COLON
        )
        
        # 远端结肠标准敏感性
        prob_distal = fit_tool.calculate_detection_probability(
            sample_individual,
            DiseaseState.HIGH_RISK_ADENOMA,
            AnatomicalLocation.DISTAL_COLON
        )
        
        assert prob_proximal < prob_distal
    
    @patch('random.random')
    def test_perform_screening(self, mock_random, fit_tool, sample_individual):
        """测试筛查执行"""
        # 模拟确定性结果
        mock_random.return_value = 0.5
        
        # 设置个体状态
        sample_individual.current_disease_state = DiseaseState.CLINICAL_CANCER_STAGE_I
        
        result = fit_tool.perform_screening(sample_individual)
        assert result in [ScreeningResult.POSITIVE, ScreeningResult.NEGATIVE, ScreeningResult.INADEQUATE]
    
    def test_hemoglobin_concentration_calculation(self, fit_tool, sample_individual):
        """测试血红蛋白浓度计算"""
        # 正常状态应该有低浓度
        conc_normal = fit_tool.calculate_hemoglobin_concentration(
            sample_individual,
            DiseaseState.NORMAL
        )
        assert conc_normal >= 0
        
        # 癌症状态应该有高浓度
        conc_cancer = fit_tool.calculate_hemoglobin_concentration(
            sample_individual,
            DiseaseState.CLINICAL_CANCER_STAGE_I
        )
        assert conc_cancer > conc_normal
    
    def test_threshold_based_detection(self, fit_tool):
        """测试基于阈值的检测"""
        # 低于阈值应该为阴性
        assert not fit_tool.is_positive_by_threshold(50.0)
        
        # 高于阈值应该为阳性
        assert fit_tool.is_positive_by_threshold(150.0)
        
        # 等于阈值应该为阳性
        assert fit_tool.is_positive_by_threshold(100.0)


class TestColonoscopyTool:
    """测试结肠镜工具"""
    
    @pytest.fixture
    def sample_individual(self):
        """创建样本个体"""
        return Individual(
            birth_year=1963,
            gender=Gender.FEMALE
        )
    
    @pytest.fixture
    def colonoscopy_tool(self):
        """创建结肠镜工具实例"""
        return ColonoscopyTool()
    
    def test_colonoscopy_tool_initialization(self, colonoscopy_tool):
        """测试结肠镜工具初始化"""
        assert colonoscopy_tool.tool_type == ScreeningToolType.COLONOSCOPY
        assert colonoscopy_tool.performance.specificity == 0.99
        assert colonoscopy_tool.characteristics.invasiveness.value == "invasive"
        assert colonoscopy_tool.characteristics.preparation_required
        assert colonoscopy_tool.characteristics.requires_sedation
    
    def test_high_sensitivity(self, colonoscopy_tool, sample_individual):
        """测试高敏感性"""
        # 结肠镜对腺瘤应该有很高的敏感性
        prob_adenoma = colonoscopy_tool.calculate_detection_probability(
            sample_individual,
            DiseaseState.HIGH_RISK_ADENOMA
        )
        assert prob_adenoma > 0.8
        
        # 对癌症应该有极高的敏感性
        prob_cancer = colonoscopy_tool.calculate_detection_probability(
            sample_individual,
            DiseaseState.CLINICAL_CANCER_STAGE_I
        )
        assert prob_cancer > 0.9
    
    def test_procedure_success_factors(self, colonoscopy_tool, sample_individual):
        """测试检查成功因素"""
        # 测试盲肠插管成功率
        success_rate = colonoscopy_tool.calculate_cecal_intubation_success(
            sample_individual,
            operator_skill=1.0
        )
        assert isinstance(success_rate, bool)
        
        # 测试退镜时间影响
        time_impact_short = colonoscopy_tool.calculate_withdrawal_time_impact(5.0)
        time_impact_adequate = colonoscopy_tool.calculate_withdrawal_time_impact(8.0)
        assert time_impact_short < time_impact_adequate


class TestSigmoidoscopyTool:
    """测试乙状结肠镜工具"""
    
    @pytest.fixture
    def sigmoidoscopy_tool(self):
        """创建乙状结肠镜工具实例"""
        return SigmoidoscopyTool()
    
    @pytest.fixture
    def sample_individual(self):
        """创建样本个体"""
        return Individual(
            birth_year=1968,
            gender=Gender.MALE
        )
    
    def test_sigmoidoscopy_tool_initialization(self, sigmoidoscopy_tool):
        """测试乙状结肠镜工具初始化"""
        assert sigmoidoscopy_tool.tool_type == ScreeningToolType.SIGMOIDOSCOPY
        assert sigmoidoscopy_tool.characteristics.invasiveness.value == "invasive"
        assert sigmoidoscopy_tool.characteristics.preparation_required
        assert not sigmoidoscopy_tool.characteristics.requires_sedation
        assert not sigmoidoscopy_tool.characteristics.can_detect_proximal
    
    def test_location_detection_capability(self, sigmoidoscopy_tool, sample_individual):
        """测试位置检测能力"""
        # 应该能检测远端结肠
        prob_distal = sigmoidoscopy_tool.calculate_detection_probability(
            sample_individual,
            DiseaseState.HIGH_RISK_ADENOMA,
            AnatomicalLocation.DISTAL_COLON
        )
        assert prob_distal > 0
        
        # 应该能检测直肠
        prob_rectal = sigmoidoscopy_tool.calculate_detection_probability(
            sample_individual,
            DiseaseState.HIGH_RISK_ADENOMA,
            AnatomicalLocation.RECTUM
        )
        assert prob_rectal > 0
        
        # 不应该能检测近端结肠
        prob_proximal = sigmoidoscopy_tool.calculate_detection_probability(
            sample_individual,
            DiseaseState.HIGH_RISK_ADENOMA,
            AnatomicalLocation.PROXIMAL_COLON
        )
        assert prob_proximal == 0
    
    def test_proximal_cancer_miss_rate(self, sigmoidoscopy_tool):
        """测试近端结肠癌症漏诊率"""
        miss_rate = sigmoidoscopy_tool.calculate_proximal_cancer_miss_rate()
        assert 0.3 <= miss_rate <= 0.5  # 应该在30-50%范围内
